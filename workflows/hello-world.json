{"name": "Hello World Example", "nodes": [{"parameters": {}, "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "12345678-1234-1234-1234-123456789012", "name": "message", "value": "Hello World from imported workflow!", "type": "string"}, {"id": "12345678-1234-1234-1234-123456789013", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "12345678-1234-1234-1234-123456789014", "name": "status", "value": "success", "type": "string"}]}, "options": {}}, "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "name": "Set Welcome Message", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 300]}], "pinData": {}, "connections": {"When clicking \"Test workflow\"": {"main": [[{"node": "Set Welcome Message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "00000000-0000-0000-0000-000000000000", "meta": {"templateCredsSetupCompleted": true}, "id": "hello-world-example", "tags": []}